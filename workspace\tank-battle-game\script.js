// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const livesElement = document.getElementById('lives');
const gameOverElement = document.getElementById('gameOver');
const finalScoreElement = document.getElementById('finalScore');
const restartBtn = document.getElementById('restartBtn');

// Game state
let score = 0;
let lives = 3;
let gameRunning = true;

// Player tank
const player = {
    x: canvas.width / 2 - 15,
    y: canvas.height - 50,
    width: 30,
    height: 30,
    speed: 5,
    color: '#3498db',
    direction: 'up'
};

// Bullets array
let bullets = [];

// Enemy tanks array
let enemies = [];

// Obstacles array
let obstacles = [];

// Key state tracking
const keys = {};

// Initialize game
function init() {
    // Create obstacles
    createObstacles();
    
    // Create initial enemies
    createEnemies(5);
    
    // Start game loop
    gameLoop();
}

// Create obstacles
function createObstacles() {
    // Create some random obstacles
    for (let i = 0; i < 10; i++) {
        obstacles.push({
            x: Math.random() * (canvas.width - 40),
            y: Math.random() * (canvas.height - 200) + 50,
            width: 40,
            height: 40,
            color: '#95a5a6'
        });
    }
}

// Create enemies
function createEnemies(count) {
    for (let i = 0; i < count; i++) {
        enemies.push({
            x: Math.random() * (canvas.width - 30),
            y: 20 + Math.random() * 100,
            width: 30,
            height: 30,
            speed: 1 + Math.random() * 2,
            color: '#e74c3c',
            direction: 'down',
            shootTimer: 0
        });
    }
}

// Draw player tank
function drawPlayer() {
    ctx.fillStyle = player.color;
    ctx.fillRect(player.x, player.y, player.width, player.height);
    
    // Draw tank barrel based on direction
    ctx.fillStyle = '#2c3e50';
    switch(player.direction) {
        case 'up':
            ctx.fillRect(player.x + 12, player.y - 10, 6, 10);
            break;
        case 'down':
            ctx.fillRect(player.x + 12, player.y + player.height, 6, 10);
            break;
        case 'left':
            ctx.fillRect(player.x - 10, player.y + 12, 10, 6);
            break;
        case 'right':
            ctx.fillRect(player.x + player.width, player.y + 12, 10, 6);
            break;
    }
}

// Draw bullets
function drawBullets() {
    bullets.forEach(bullet => {
        ctx.fillStyle = bullet.color;
        ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
    });
}

// Draw enemies
function drawEnemies() {
    enemies.forEach(enemy => {
        ctx.fillStyle = enemy.color;
        ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
        
        // Draw enemy tank barrel
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(enemy.x + 12, enemy.y + enemy.height, 6, 10);
    });
}

// Draw obstacles
function drawObstacles() {
    obstacles.forEach(obstacle => {
        ctx.fillStyle = obstacle.color;
        ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
    });
}

// Update player position
function updatePlayer() {
    if (keys['ArrowUp'] && player.y > 0) {
        player.y -= player.speed;
        player.direction = 'up';
    }
    if (keys['ArrowDown'] && player.y < canvas.height - player.height) {
        player.y += player.speed;
        player.direction = 'down';
    }
    if (keys['ArrowLeft'] && player.x > 0) {
        player.x -= player.speed;
        player.direction = 'left';
    }
    if (keys['ArrowRight'] && player.x < canvas.width - player.width) {
        player.x += player.speed;
        player.direction = 'right';
    }
}

// Update bullets
function updateBullets() {
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];
        
        // Move bullet
        switch(bullet.direction) {
            case 'up':
                bullet.y -= bullet.speed;
                break;
            case 'down':
                bullet.y += bullet.speed;
                break;
            case 'left':
                bullet.x -= bullet.speed;
                break;
            case 'right':
                bullet.x += bullet.speed;
                break;
        }
        
        // Remove bullets that go off screen
        if (bullet.x < 0 || bullet.x > canvas.width || 
            bullet.y < 0 || bullet.y > canvas.height) {
            bullets.splice(i, 1);
            continue;
        }
        
        // Check collision with obstacles
        for (let j = obstacles.length - 1; j >= 0; j--) {
            if (checkCollision(bullet, obstacles[j])) {
                bullets.splice(i, 1);
                obstacles.splice(j, 1);
                break;
            }
        }
    }
}

// Update enemies
function updateEnemies() {
    enemies.forEach(enemy => {
        // Move enemy
        enemy.y += enemy.speed;
        
        // Change direction randomly
        if (Math.random() < 0.01) {
            enemy.speed = 1 + Math.random() * 2;
        }
        
        // Reset enemy position if it goes off screen
        if (enemy.y > canvas.height) {
            enemy.y = -30;
            enemy.x = Math.random() * (canvas.width - 30);
        }
        
        // Enemy shooting
        enemy.shootTimer++;
        if (enemy.shootTimer > 120) { // Shoot every 2 seconds at 60fps
            shootBullet(enemy.x + 15, enemy.y + 30, 'down', '#e74c3c');
            enemy.shootTimer = 0;
        }
    });
}

// Shoot bullet
function shootBullet(x, y, direction, color) {
    bullets.push({
        x: x,
        y: y,
        width: 5,
        height: 10,
        speed: 7,
        direction: direction,
        color: color
    });
}

// Check collision between two objects
function checkCollision(obj1, obj2) {
    return obj1.x < obj2.x + obj2.width &&
           obj1.x + obj1.width > obj2.x &&
           obj1.y < obj2.y + obj2.height &&
           obj1.y + obj1.height > obj2.y;
}

// Check all collisions
function checkCollisions() {
    // Player bullets hitting enemies
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];
        
        // Only player bullets (blue) can hit enemies
        if (bullet.color === '#3498db') {
            for (let j = enemies.length - 1; j >= 0; j--) {
                const enemy = enemies[j];
                if (checkCollision(bullet, enemy)) {
                    // Remove bullet and enemy
                    bullets.splice(i, 1);
                    enemies.splice(j, 1);
                    
                    // Increase score
                    score += 100;
                    scoreElement.textContent = score;
                    
                    // Create new enemy
                    createEnemies(1);
                    break;
                }
            }
        }
    }
    
    // Enemy bullets hitting player
    for (let i = bullets.length - 1; i >= 0; i--) {
        const bullet = bullets[i];
        
        // Only enemy bullets (red) can hit player
        if (bullet.color === '#e74c3c' && checkCollision(bullet, player)) {
            bullets.splice(i, 1);
            lives--;
            livesElement.textContent = lives;
            
            if (lives <= 0) {
                gameOver();
            }
            break;
        }
    }
    
    // Player colliding with enemies
    for (let i = 0; i < enemies.length; i++) {
        if (checkCollision(player, enemies[i])) {
            lives--;
            livesElement.textContent = lives;
            
            // Move enemy to top
            enemies[i].y = -30;
            enemies[i].x = Math.random() * (canvas.width - 30);
            
            if (lives <= 0) {
                gameOver();
            }
            break;
        }
    }
}

// Game over
function gameOver() {
    gameRunning = false;
    finalScoreElement.textContent = score;
    gameOverElement.style.display = 'block';
}

// Restart game
function restartGame() {
    // Reset game state
    score = 0;
    lives = 3;
    gameRunning = true;
    
    // Reset UI
    scoreElement.textContent = score;
    livesElement.textContent = lives;
    gameOverElement.style.display = 'none';
    
    // Clear arrays
    bullets = [];
    enemies = [];
    obstacles = [];
    
    // Reset player position
    player.x = canvas.width / 2 - 15;
    player.y = canvas.height - 50;
    
    // Recreate game objects
    createObstacles();
    createEnemies(5);
}

// Game loop
function gameLoop() {
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (gameRunning) {
        // Update game objects
        updatePlayer();
        updateBullets();
        updateEnemies();
        checkCollisions();
        
        // Draw everything
        drawObstacles();
        drawBullets();
        drawEnemies();
        drawPlayer();
    }
    
    // Continue game loop
    requestAnimationFrame(gameLoop);
}

// Event listeners
document.addEventListener('keydown', (e) => {
    keys[e.key] = true;
    
    // Shoot when space is pressed
    if (e.key === ' ' && gameRunning) {
        switch(player.direction) {
            case 'up':
                shootBullet(player.x + 15, player.y, 'up', '#3498db');
                break;
            case 'down':
                shootBullet(player.x + 15, player.y + player.height, 'down', '#3498db');
                break;
            case 'left':
                shootBullet(player.x, player.y + 15, 'left', '#3498db');
                break;
            case 'right':
                shootBullet(player.x + player.width, player.y + 15, 'right', '#3498db');
                break;
        }
    }
});

document.addEventListener('keyup', (e) => {
    keys[e.key] = false;
});

restartBtn.addEventListener('click', restartGame);

// Start the game
init();