* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #2c3e50;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: white;
}

.game-container {
    text-align: center;
    background-color: #34495e;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #ecf0f1;
    font-size: 2.5em;
}

.score-board {
    font-size: 1.2em;
    background-color: #2c3e50;
    padding: 10px 20px;
    border-radius: 5px;
}

.score-board span {
    margin: 0 10px;
}

#gameCanvas {
    background-color: #1a1a1a;
    border: 3px solid #7f8c8d;
    border-radius: 5px;
}

.controls {
    margin-top: 15px;
    font-size: 1em;
    color: #bdc3c7;
}

.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(44, 62, 80, 0.95);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
}

.game-over h2 {
    font-size: 2.5em;
    color: #e74c3c;
    margin-bottom: 20px;
}

.game-over p {
    font-size: 1.5em;
    margin-bottom: 20px;
}

#restartBtn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.2em;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#restartBtn:hover {
    background-color: #2980b9;
}