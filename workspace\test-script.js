function testFunction() {
    const resultDiv = document.getElementById('result');
    resultDiv.innerHTML = '🎉 JavaScript正常工作！CSS和JS文件都已成功加载。';
    resultDiv.style.display = 'block';
    
    // 添加一些动画效果
    resultDiv.style.opacity = '0';
    resultDiv.style.transition = 'opacity 0.5s ease-in-out';
    
    setTimeout(() => {
        resultDiv.style.opacity = '1';
    }, 100);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，JavaScript文件已成功加载！');
    
    // 检查CSS是否加载成功
    const container = document.querySelector('.container');
    if (container) {
        const styles = window.getComputedStyle(container);
        if (styles.backgroundColor === 'rgb(255, 255, 255)') {
            console.log('CSS文件已成功加载！');
        }
    }
});
