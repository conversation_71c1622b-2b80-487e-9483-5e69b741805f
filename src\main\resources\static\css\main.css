/* SpringAI Alibaba Copilot - 主样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    width: 90%;
    max-width: 1200px;
    height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin-bottom: 10px;
    font-size: 24px;
}

.header p {
    opacity: 0.9;
    font-size: 14px;
}

.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.chat-area {
    flex: 2;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #eee;
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.user > div {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 18px 18px 4px 18px;
}

.message.assistant > div {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 18px 18px 18px 4px;
}

.message > div {
    max-width: 70%;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-role {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
    opacity: 0.8;
}

.message-content {
    line-height: 1.5;
    word-wrap: break-word;
}

.message-content pre {
    background: #f4f4f4;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    overflow-x: auto;
    font-size: 13px;
}

.message-content code {
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.input-area {
    padding: 20px;
    background: white;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
}

.input-area input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.input-area input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.input-area button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.input-area button:first-of-type {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.input-area button:first-of-type:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.clear-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.clear-btn:hover {
    background: #e9ecef;
    color: #333;
}

.input-area button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.sidebar {
    flex: 1;
    padding: 20px;
    background: #f8f9fa;
    overflow-y: auto;
}

.sidebar h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 30px;
}

.quick-action {
    padding: 12px 16px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    color: #333;
}

.quick-action:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.status.success {
    background: #28a745;
}

.status.error {
    background: #dc3545;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 工具日志容器样式 */
.tool-log-container {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin: 15px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.tool-log-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-log-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.connection-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background: #e9ecef;
    color: #666;
}

.connection-status.connected {
    background: #d4edda;
    color: #155724;
}

.connection-status.error {
    background: #f8d7da;
    color: #721c24;
}

.connection-status.completed {
    background: #d1ecf1;
    color: #0c5460;
}

.tool-log-content {
    padding: 16px;
}

.tool-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.tool-card.running {
    border-left: 4px solid #ffc107;
    background: #fff8e1;
}

.tool-card.success {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.tool-card.error {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.tool-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.tool-icon {
    font-size: 16px;
}

.tool-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.tool-status {
    margin-left: auto;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: #e9ecef;
    color: #666;
}

.tool-status.success {
    background: #d4edda;
    color: #155724;
}

.tool-status.error {
    background: #f8d7da;
    color: #721c24;
}

.tool-file {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.tool-message {
    font-size: 13px;
    color: #333;
    margin-bottom: 4px;
}

.tool-time {
    font-size: 11px;
    color: #888;
}

/* 等待状态卡片样式 */
.tool-log-container.waiting {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border: 2px dashed #4285f4;
    animation: waitingPulse 2s ease-in-out infinite;
}

.waiting-message {
    text-align: center;
    padding: 20px;
    color: #666;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #e3e3e3;
    border-top: 3px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

.waiting-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #4285f4;
}

.waiting-hint {
    font-size: 12px;
    color: #888;
    line-height: 1.4;
}

.connection-status.connecting {
    color: #4285f4;
    animation: blink 1.5s ease-in-out infinite;
}

/* 改进加载状态样式 */
.loading.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* 动画定义 */
@keyframes waitingPulse {
    0%, 100% {
        border-color: #4285f4;
        box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.3);
    }
    50% {
        border-color: #34a853;
        box-shadow: 0 0 0 8px rgba(66, 133, 244, 0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        width: 95%;
        height: 90vh;
    }

    .main-content {
        flex-direction: column;
    }

    .chat-area {
        border-right: none;
        border-bottom: 1px solid #eee;
    }

    .sidebar {
        max-height: 200px;
    }

    .message > div {
        max-width: 85%;
    }

    .header h1 {
        font-size: 20px;
    }

    .header p {
        font-size: 12px;
    }

    .input-area {
        padding: 15px;
    }

    .input-area input {
        font-size: 16px; /* 防止iOS缩放 */
    }

    .quick-action {
        padding: 10px 12px;
        font-size: 12px;
    }

    .waiting-message {
        padding: 15px;
    }

    .waiting-text {
        font-size: 13px;
    }

    .waiting-hint {
        font-size: 11px;
    }

    .tool-log-container {
        margin: 10px 0;
    }

    .tool-log-content {
        padding: 12px;
    }

    .tool-card {
        padding: 10px;
        margin-bottom: 10px;
    }
}

/* 流式消息样式 */
.message.streaming {
    position: relative;
}

.stream-content {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.5;
}

.stream-indicator {
    display: flex;
    align-items: center;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e0e0e0;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-indicator span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #007bff;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.stream-indicator .error {
    color: #dc3545;
    font-size: 12px;
    font-style: italic;
}

/* 工具配置模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border: none;
    border-radius: 12px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
}

.close {
    color: white;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.2s;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 30px;
}

/* 标签页样式 */
.tab-nav {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 30px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #333;
    background: #f5f5f5;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9ff;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 工具区域样式 */
.tool-section {
    margin-bottom: 30px;
    padding: 25px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    background: #f9f9f9;
}

.tool-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
}

.tools-list {
    display: grid;
    gap: 15px;
}

.tool-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.tool-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tool-item.active {
    border-color: #4CAF50;
    background: #f0fff0;
}

.tool-info {
    flex: 1;
}

.tool-name {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.tool-icon {
    font-size: 20px;
}

.tool-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.tool-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.tool-badge.system {
    background: #e3f2fd;
    color: #1976d2;
}

.tool-badge.mcp {
    background: #f3e5f5;
    color: #7b1fa2;
}

.tool-description {
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.tool-source {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
}

.tool-parameters {
    font-size: 12px;
    color: #666;
    font-family: monospace;
    background: #f5f5f5;
    padding: 5px 8px;
    border-radius: 4px;
}

.tool-actions {
    display: flex;
    gap: 8px;
    flex-direction: column;
}

.tool-actions button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.2s;
    min-width: 60px;
}

.btn-test {
    background: #2196F3;
    color: white;
}

.btn-test:hover {
    background: #1976D2;
}

.btn-info {
    background: #FF9800;
    color: white;
}

.btn-info:hover {
    background: #F57C00;
}

.btn-edit {
    background: #FF9800;
    color: white;
}

.btn-edit:hover {
    background: #F57C00;
}

.btn-delete {
    background: #f44336;
    color: white;
}

.btn-delete:hover {
    background: #d32f2f;
}

.btn-toggle {
    background: #4CAF50;
    color: white;
}

.btn-toggle:hover {
    background: #388E3C;
}

/* 配置方法样式 */
.config-method {
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.config-method h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-group textarea {
    height: 100px;
    resize: vertical;
    font-family: monospace;
}

#mcpJsonConfig {
    height: 150px;
    font-family: monospace;
    font-size: 13px;
}

.config-method button,
.form-group button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s;
}

.config-method button:hover,
.form-group button:hover {
    background: #5a67d8;
}

/* 加载和空状态样式 */
.loading-tools,
.no-tools {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.no-tools small {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #999;
}

/* 测试结果模态框 */
.test-result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.test-result-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.test-result-header {
    background: #f5f5f5;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.test-result-header h3 {
    margin: 0;
    color: #333;
}

.test-result-header button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.test-result-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.test-result-body pre {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 13px;
    line-height: 1.4;
}

/* 工具配置响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 1% auto;
    }

    .modal-body {
        padding: 20px;
    }

    .tab-nav {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: 12px 15px;
        font-size: 14px;
    }

    .tool-item {
        flex-direction: column;
        gap: 15px;
    }

    .tool-actions {
        flex-direction: row;
        justify-content: flex-end;
    }

    .tool-actions button {
        min-width: 50px;
        padding: 5px 10px;
    }

    .config-method {
        padding: 15px;
    }

    .test-result-content {
        max-width: 95%;
        margin: 0 10px;
    }
}
