<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpringAI Alibaba Copilot</title>
    <link rel="stylesheet" href="/css/main.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 SpringAI Alibaba 编码助手</h1>
            <p>AI助手将分析您的需求，制定执行计划，并逐步完成任务</p>
        </div>

        <div class="main-content">
            <div class="chat-area">
                <div class="messages" id="messages">
                    <div class="message assistant">
                        <div>
                            <div class="message-role">Assistant</div>
                            <div class="message-content">
                                👋 Hello! I'm your AI file operations assistant. I can help you:
                                <br><br>
                                📁 <strong>Read files</strong> - View file contents with pagination support
                                <br>✏️ <strong>Write files</strong> - Create new files or overwrite existing ones
                                <br>🔧 <strong>Edit files</strong> - Make precise edits with diff preview
                                <br>📋 <strong>List directories</strong> - Browse directory structure
                                <br><br>
                                Try asking me to create a simple project, read a file, or explore the workspace!
                                <br><br>
                                <em>Workspace: /workspace</em>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="loading" id="loading">
                    <div>🤔 AI is thinking...</div>
                </div>

                <div class="input-area">
                    <input type="text" id="messageInput" placeholder="Ask me to create files, read content, or manage your project..." />
                    <button onclick="sendMessage()" id="sendBtn">Send</button>
                    <button onclick="clearHistory()" class="clear-btn" id="clearBtn">Clear</button>
                </div>
            </div>

            <div class="sidebar">
                <h3>🚀 Quick Actions</h3>
                <div class="quick-actions">
                    <div class="quick-action" onclick="quickAction('List the workspace directory')">
                        📁 List workspace directory
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a simple Java Hello World project')">
                        ☕ Create Java Hello World
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a simple web project with HTML, CSS and JS')">
                        🌐 Create web project
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a README.md file for this project')">
                        📝 Create README.md
                    </div>
                    <div class="quick-action" onclick="quickAction('Show me the structure of the current directory recursively')">
                        🌳 Show directory tree
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a simple Python script that prints hello world')">
                        🐍 Create Python script
                    </div>
                </div>

                <h3>🔄 Continuous Task Tests</h3>
                <div class="quick-actions">
                    <div class="quick-action" onclick="quickAction('Create a complete React project with components, styles, and package.json')">
                        ⚛️ Create React project
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a full-stack todo app with HTML, CSS, JavaScript frontend and Node.js backend')">
                        📋 Create Todo App
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a Spring Boot REST API project with controller, service, and model classes')">
                        🍃 Create Spring Boot API
                    </div>
                    <div class="quick-action" onclick="quickAction('Create a complete blog website with multiple HTML pages, CSS styles, and JavaScript functionality')">
                        📰 Create Blog Website
                    </div>
                </div>

                <div id="status" class="status" style="display: none;"></div>

                <!-- 新增：工具配置入口 -->
                <h3>🔧 工具配置</h3>
                <div class="quick-actions">
                    <div class="quick-action" onclick="openToolConfig()">
                        ⚙️ 配置动态工具
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h3>💡 Tips</h3>
                    <div style="font-size: 12px; color: #666; line-height: 1.4;">
                        • Ask for step-by-step project creation<br>
                        • Request file content before editing<br>
                        • Use specific file paths<br>
                        • Ask for directory structure first<br>
                        • Try continuous operations
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具配置模态框 -->
    <div id="toolConfigModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔧 动态工具配置</h2>
                <span class="close" onclick="closeToolConfig()">&times;</span>
            </div>

            <div class="modal-body">
                <!-- 标签页导航 -->
                <div class="tab-nav">
                    <button class="tab-btn active" onclick="switchTab('system-tools')">系统工具</button>
                    <button class="tab-btn" onclick="switchTab('mcp-tools')">MCP工具</button>
                    <button class="tab-btn" onclick="switchTab('add-mcp')">添加MCP服务器</button>
                </div>

                <!-- 系统工具标签页 -->
                <div id="system-tools" class="tab-content active">
                    <div class="tool-section">
                        <h3>系统内置工具</h3>
                        <div id="systemToolsList" class="tools-list">
                            <div class="loading-tools">正在加载系统工具...</div>
                        </div>
                    </div>
                </div>

                <!-- MCP工具标签页 -->
                <div id="mcp-tools" class="tab-content">
                    <div class="tool-section">
                        <h3>MCP服务器工具</h3>
                        <div id="mcpToolsList" class="tools-list">
                            <div class="loading-tools">正在加载MCP工具...</div>
                        </div>
                    </div>
                </div>

                <!-- 添加MCP服务器标签页 -->
                <div id="add-mcp" class="tab-content">
                    <div class="tool-section">
                        <h3>添加MCP服务器</h3>

                        <!-- JSON配置方式 -->
                        <div class="config-method">
                            <h4>方式1: JSON配置</h4>
                            <textarea id="mcpJsonConfig" placeholder='请输入MCP服务器配置JSON:
{
  "mcpServers": {
    "mcp-server-chart": {
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart"]
    }
  }
}'></textarea>
                            <button onclick="addMcpFromJson()">从JSON添加</button>
                        </div>

                        <!-- 表单配置方式 -->
                        <div class="config-method">
                            <h4>方式2: 表单配置</h4>
                            <form id="mcpServerForm">
                                <div class="form-group">
                                    <label>服务器名称:</label>
                                    <input type="text" id="mcpServerName" required>
                                </div>
                                <div class="form-group">
                                    <label>命令:</label>
                                    <input type="text" id="mcpCommand" placeholder="npx" required>
                                </div>
                                <div class="form-group">
                                    <label>参数 (每行一个):</label>
                                    <textarea id="mcpArgs" placeholder="-y&#10;@antv/mcp-server-chart"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>工作目录:</label>
                                    <input type="text" id="mcpWorkDir" placeholder="留空使用当前目录">
                                </div>
                                <button type="submit">添加MCP服务器</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件引用 -->
    <script src="/js/tool-log-display.js"></script>
    <script src="/js/sse-manager.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/tool-config.js"></script>
</body>
</html>
