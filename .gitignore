# 编译输出
target/
dist/
build/

# 依赖目录
node_modules/

# IDE文件
.idea/
.vscode/
*.iml
*.ipr
*.iws

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# PID文件
*.pid
.backend.pid
.frontend.pid

# Maven
.mvn/
mvnw
mvnw.cmd

# Spring Boot
spring-boot-*.jar

# Vue.js
.nuxt/
.output/
.nitro/

# 测试覆盖率
coverage/
*.lcov

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件中的敏感信息
application-local.properties
application-dev.properties
application-prod.properties
