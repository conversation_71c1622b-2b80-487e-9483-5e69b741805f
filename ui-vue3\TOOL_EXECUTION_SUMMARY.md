# 工具执行概要功能

## 功能概述

为每个工具执行前添加执行概要展示，让用户了解AI即将执行什么操作以及为什么要执行这个操作。

## 新增功能

### 1. 工具执行概要
- **执行前说明**：在每个工具执行前显示即将执行的操作
- **执行原因**：解释为什么需要执行这个工具
- **蓝色主题**：使用蓝色主题区别于分析步骤的紫色主题

### 2. 完整的执行流程展示
```
🧠 任务分析          ✅ 完成
💡 需求理解          ✅ 完成  
📋 执行计划          ✅ 完成

📁 工具执行概要      📋 规划中
   即将列出目录 'workspace' 的文件结构
   原因：需要了解 'workspace' 的目录结构来规划后续操作

📁 listDirectory    ⏳ 执行中
   正在列出目录: workspace

📁 listDirectory    ✅ 成功
   已列出目录 workspace (耗时 45ms)

✏️ 工具执行概要      📋 规划中
   即将创建/写入文件 'index.html'
   原因：需要创建/写入 'index.html' 来实现功能需求

✏️ writeFile        ⏳ 执行中
   正在写入文件: index.html

✏️ writeFile        ✅ 成功
   已写入文件 index.html (耗时 123ms)
```

## 技术实现

### 后端实现

#### 1. 新增工具执行概要事件
```java
// LogStreamService.java
public void pushToolExecutionSummary(String taskId, String toolName, String filePath, 
                                   String summary, String reason) {
    AnalysisEvent event = new AnalysisEvent();
    event.setType("TOOL_EXECUTION_SUMMARY");
    event.setStepName("工具执行概要");
    event.setDescription(summary);
    event.setDetails(reason);
    event.setStatus("PLANNING");
    // ...
}
```

#### 2. AOP切面集成
```java
// ToolCallLoggingAspect.java
@Around("@annotation(org.springframework.ai.tool.annotation.Tool)")
public Object interceptToolAnnotation(ProceedingJoinPoint joinPoint) throws Throwable {
    // 1. 推送工具执行概要
    String summary = generateExecutionSummary(methodName, fileInfo, args);
    String reason = generateExecutionReason(methodName, fileInfo, args);
    logStreamService.pushToolExecutionSummary(taskId, methodName, fileInfo, summary, reason);
    
    // 2. 短暂延迟让前端显示概要
    Thread.sleep(200);
    
    // 3. 推送工具开始执行
    logStreamService.pushToolStart(taskId, methodName, fileInfo, startMessage);
    
    // 4. 执行工具
    Object result = joinPoint.proceed();
    
    // 5. 推送执行结果
    logStreamService.pushToolSuccess(taskId, methodName, fileInfo, successMessage, executionTime);
}
```

#### 3. 智能概要生成
```java
private String generateExecutionSummary(String toolName, String fileInfo, Object[] args) {
    switch (toolName) {
        case "readFile":
            return String.format("即将读取文件 '%s' 的内容", fileName);
        case "writeFile":
            return String.format("即将创建/写入文件 '%s'", fileName);
        case "editFile":
            return String.format("即将编辑文件 '%s' 的内容", fileName);
        // ...
    }
}

private String generateExecutionReason(String toolName, String fileInfo, Object[] args) {
    switch (toolName) {
        case "readFile":
            return String.format("需要读取 '%s' 来了解当前文件内容，以便进行后续操作", fileName);
        case "writeFile":
            return String.format("需要创建/写入 '%s' 来实现功能需求", fileName);
        // ...
    }
}
```

### 前端实现

#### 1. 事件类型扩展
```typescript
// 在processedAnalysisSteps中包含工具执行概要
if (event.type === 'TASK_ANALYSIS_START' || 
    event.type === 'ANALYSIS_STEP' || 
    event.type === 'EXECUTION_PLAN' ||
    event.type === 'TOOL_EXECUTION_SUMMARY') {
  analysisSteps.push({
    ...event,
    status: event.status || 'COMPLETED'
  })
}
```

#### 2. 新增PLANNING状态
```javascript
const getAnalysisStatusText = (status: string) => {
  switch (status) {
    case 'ANALYZING': return '🔍 分析中'
    case 'PLANNING': return '📋 规划中'  // 新增
    case 'COMPLETED': return '✅ 完成'
    case 'ERROR': return '❌ 失败'
  }
}
```

#### 3. 特殊样式设计
```css
/* 工具执行概要的蓝色主题 */
.analysis-card[data-type="TOOL_EXECUTION_SUMMARY"]::before {
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
}

.analysis-card[data-type="TOOL_EXECUTION_SUMMARY"] {
  border-left: 3px solid #3b82f6;
}
```

## 用户体验改进

### 1. 透明度提升
- **操作预告**：用户提前知道AI即将执行什么操作
- **原因说明**：理解为什么需要执行这个操作
- **心理准备**：减少突然执行工具的意外感

### 2. 学习价值
- **工作流程理解**：学习AI的工作思路和步骤
- **工具使用场景**：了解不同工具的使用时机
- **问题解决思路**：学习如何分解复杂任务

### 3. 信任建立
- **决策透明**：AI的每个决策都有明确说明
- **逻辑清晰**：操作步骤和原因逻辑清晰
- **可预测性**：用户可以预期接下来会发生什么

## 示例场景

### 场景1：创建Vue项目
```
📋 执行计划 ✅ 完成
   已生成执行计划：分析项目结构 → 创建必要文件 → 实现核心功能

📁 工具执行概要 📋 规划中
   即将列出目录 'workspace' 的文件结构
   原因：需要了解 'workspace' 的目录结构来规划后续操作

📁 listDirectory ✅ 成功

✏️ 工具执行概要 📋 规划中
   即将创建/写入文件 'package.json'
   原因：需要创建/写入 'package.json' 来实现功能需求

✏️ writeFile ✅ 成功
```

### 场景2：编辑现有文件
```
📖 工具执行概要 📋 规划中
   即将读取文件 'src/App.vue' 的内容
   原因：需要读取 'App.vue' 来了解当前文件内容，以便进行后续操作

📖 readFile ✅ 成功

📝 工具执行概要 📋 规划中
   即将编辑文件 'src/App.vue' 的内容
   原因：需要修改 'App.vue' 的内容来完善功能或修复问题

📝 editFile ✅ 成功
```

这个功能让AI的工作过程更加透明和可理解，用户可以清楚地看到AI的每一步操作计划和执行过程。
